using UnityEngine;
using Sirenix.OdinInspector;

[CreateAssetMenu(fileName = "VegetationPrefab", menuName = "2D Rogue/Vegetation Prefab Data")]
public class VegetationPrefabData : ScriptableObject
{
    [Title("Vegetation Prefab Settings")]
    [FoldoutGroup("General")]
    [SerializeField]
    private string vegetationName = "New Vegetation";
    
    [FoldoutGroup("General")]
    [SerializeField, TextArea(2, 4)]
    private string description;
    
    [FoldoutGroup("General")]
    [SerializeField]
    [AssetsOnly]
    [Required("Prefab is required")]
    private GameObject prefab;

    [Title("Sprite-Based System")]
    [FoldoutGroup("Sprite System")]
    [SerializeField]
    [Tooltip("Enable sprite-based vegetation (uses VegetationType instead of fixed prefab sprites)")]
    private bool useSpriteBasedSystem = false;

    [FoldoutGroup("Sprite System")]
    [SerializeField]
    [ShowIf("useSpriteBasedSystem")]
    [Required("VegetationType is required when using sprite-based system")]
    [AssetsOnly]
    [Tooltip("Vegetation type defining available sprites and properties")]
    private VegetationType vegetationType;

    [FoldoutGroup("Sprite System")]
    [SerializeField]
    [ShowIf("useSpriteBasedSystem")]
    [Tooltip("Override vegetation type's scale multiplier")]
    private bool overrideTypeScale = false;

    [FoldoutGroup("Sprite System")]
    [SerializeField]
    [ShowIf("@useSpriteBasedSystem && overrideTypeScale")]
    [Range(0.1f, 3f)]
    [Tooltip("Custom scale multiplier for this prefab data")]
    private float customScaleMultiplier = 1f;
    
    [Title("Spawning Settings")]
    [FoldoutGroup("Spawning")]
    [SerializeField, Range(0f, 1f)]
    [Tooltip("Base chance to spawn this vegetation type")]
    private float spawnChance = 0.3f;
    
    [FoldoutGroup("Spawning")]
    [SerializeField]
    [Tooltip("Minimum distance between instances of this vegetation")]
    private float minimumSpacing = 0.5f;
    
    [FoldoutGroup("Spawning")]
    [SerializeField]
    [Tooltip("Can spawn on tile edges or only centers")]
    private bool allowEdgePlacement = true;
    
    [Title("Visual Variation")]
    [FoldoutGroup("Variation")]
    [SerializeField]
    [MinMaxSlider(0.5f, 2f, true)]
    [Tooltip("Random scale range for visual variety")]
    private Vector2 scaleRange = new Vector2(0.9f, 1.1f);
    
    [FoldoutGroup("Variation")]
    [SerializeField]
    [Tooltip("Random rotation on spawn")]
    private bool randomRotation = false;
    
    [FoldoutGroup("Variation")]
    [SerializeField]
    [ShowIf("randomRotation")]
    [MinMaxSlider(0f, 360f, true)]
    private Vector2 rotationRange = new Vector2(0f, 360f);
    
    [FoldoutGroup("Variation")]
    [SerializeField]
    [Tooltip("Random sprite flip for variety")]
    private bool allowRandomFlip = true;
    
    [Title("Y-Sorting")]
    [FoldoutGroup("Sorting")]
    [SerializeField]
    [Tooltip("Y-offset for sorting (negative values sort behind player)")]
    private float ySortingOffset = 0f;
    
    [FoldoutGroup("Sorting")]
    [SerializeField]
    [Tooltip("Base sorting order offset")]
    private int sortingOrderOffset = 0;
    
    [Title("Performance")]
    [FoldoutGroup("Performance")]
    [SerializeField]
    [Tooltip("Maximum instances per chunk")]
    private int maxPerChunk = 50;
    
    [FoldoutGroup("Performance")]
    [SerializeField]
    [Tooltip("Initial pool size for this vegetation type")]
    private int initialPoolSize = 100;
    
    [FoldoutGroup("Performance")]
    [SerializeField]
    [Tooltip("Pool grow size when exhausted")]
    private int poolGrowSize = 20;
    
    // Properties
    public string VegetationName => vegetationName;
    public GameObject Prefab => prefab;
    public float SpawnChance => spawnChance;
    public float MinimumSpacing => minimumSpacing;
    public bool AllowEdgePlacement => allowEdgePlacement;
    public Vector2 ScaleRange => scaleRange;
    public bool RandomRotation => randomRotation;
    public Vector2 RotationRange => rotationRange;
    public bool AllowRandomFlip => allowRandomFlip;
    public float YSortingOffset => ySortingOffset;
    public int SortingOrderOffset => sortingOrderOffset;
    public int MaxPerChunk => maxPerChunk;
    public int InitialPoolSize => initialPoolSize;
    public int PoolGrowSize => poolGrowSize;

    // Sprite-based system properties
    public bool UseSpriteBasedSystem => useSpriteBasedSystem;
    public VegetationType VegetationType => vegetationType;
    public bool OverrideTypeScale => overrideTypeScale;
    public float CustomScaleMultiplier => customScaleMultiplier;
    
    public float GetRandomScale()
    {
        return Random.Range(scaleRange.x, scaleRange.y);
    }
    
    public Quaternion GetRandomRotation()
    {
        if (!randomRotation) return Quaternion.identity;
        
        float angle = Random.Range(rotationRange.x, rotationRange.y);
        return Quaternion.Euler(0f, 0f, angle);
    }
    
    public bool GetRandomFlip()
    {
        return allowRandomFlip && Random.value > 0.5f;
    }

    /// <summary>
    /// Gets the effective scale multiplier, considering both local settings and vegetation type
    /// </summary>
    public float GetEffectiveScaleMultiplier()
    {
        if (!useSpriteBasedSystem || vegetationType == null)
            return 1f;

        if (overrideTypeScale)
            return customScaleMultiplier;

        return vegetationType.ScaleMultiplier;
    }

    /// <summary>
    /// Gets a random sprite from the vegetation type (sprite-based system only)
    /// </summary>
    public Sprite GetRandomSprite()
    {
        if (!useSpriteBasedSystem || vegetationType == null)
        {
            Debug.LogWarning($"VegetationPrefabData '{vegetationName}' is not using sprite-based system!");
            return null;
        }

        return vegetationType.GetRandomSprite();
    }

    /// <summary>
    /// Gets the idle animation from the vegetation type (sprite-based system only)
    /// </summary>
    public SpriteAnimation GetIdleAnimation()
    {
        if (!useSpriteBasedSystem || vegetationType == null)
            return null;

        return vegetationType.IdleAnimation;
    }

    /// <summary>
    /// Gets the wind animation from the vegetation type (sprite-based system only)
    /// </summary>
    public SpriteAnimation GetWindAnimation()
    {
        if (!useSpriteBasedSystem || vegetationType == null)
            return null;

        return vegetationType.WindAnimation;
    }

    /// <summary>
    /// Gets the sorting layer name from vegetation type or uses default
    /// </summary>
    public string GetSortingLayerName()
    {
        if (useSpriteBasedSystem && vegetationType != null)
            return vegetationType.SortingLayerName;

        return "Default";
    }

    /// <summary>
    /// Gets the effective sorting order combining base offset and vegetation type
    /// </summary>
    public int GetEffectiveSortingOrder()
    {
        int baseOrder = sortingOrderOffset;

        if (useSpriteBasedSystem && vegetationType != null)
            baseOrder += vegetationType.BaseSortingOrder;

        return baseOrder;
    }

    /// <summary>
    /// Gets the tint color from vegetation type or returns white
    /// </summary>
    public Color GetTintColor()
    {
        if (useSpriteBasedSystem && vegetationType != null)
            return vegetationType.TintColor;

        return Color.white;
    }

    /// <summary>
    /// Validates the configuration for both traditional and sprite-based systems
    /// </summary>
    public bool IsValidConfiguration()
    {
        if (prefab == null)
            return false;

        if (useSpriteBasedSystem)
        {
            if (vegetationType == null)
                return false;

            return vegetationType.IsValid();
        }

        // Traditional system validation
        return prefab.GetComponent<SpriteRenderer>() != null;
    }
    
    #if UNITY_EDITOR
    [Button("Validate Configuration", ButtonSizes.Medium)]
    void ValidateConfiguration()
    {
        Debug.Log($"=== Vegetation Configuration Validation: {vegetationName} ===");

        if (prefab == null)
        {
            Debug.LogError($"No prefab assigned to {vegetationName}!");
            return;
        }

        Debug.Log($"Prefab: {prefab.name}");
        Debug.Log($"System Type: {(useSpriteBasedSystem ? "<color=cyan>SPRITE-BASED</color>" : "<color=yellow>TRADITIONAL</color>")}");

        bool hasRenderer = prefab.GetComponent<SpriteRenderer>() != null;
        bool hasVegetationInstance = prefab.GetComponent<VegetationInstance>() != null;
        bool hasYSorting = prefab.GetComponent<YSortingController>() != null;

        Debug.Log($"Has SpriteRenderer: {(hasRenderer ? "<color=green>YES</color>" : "<color=red>NO</color>")}");
        Debug.Log($"Has VegetationInstance: {(hasVegetationInstance ? "<color=green>YES</color>" : "<color=red>NO</color>")}");
        Debug.Log($"Has YSortingController: {(hasYSorting ? "<color=green>YES</color>" : "<color=yellow>NO (will be added at runtime)</color>")}");

        if (useSpriteBasedSystem)
        {
            if (vegetationType == null)
            {
                Debug.LogError("Sprite-based system enabled but no VegetationType assigned!");
            }
            else
            {
                Debug.Log($"VegetationType: {vegetationType.name}");
                Debug.Log($"Sprite Variants: {vegetationType.GetVariantCount()}");
                Debug.Log($"Type Valid: {(vegetationType.IsValid() ? "<color=green>YES</color>" : "<color=red>NO</color>")}");
                Debug.Log($"Scale Multiplier: {GetEffectiveScaleMultiplier():F2}");
                Debug.Log($"Tint Color: {GetTintColor()}");
            }
        }

        Debug.Log($"Initial Pool Size: {initialPoolSize}");
        Debug.Log($"Max Per Chunk: {maxPerChunk}");
        Debug.Log($"Overall Valid: {(IsValidConfiguration() ? "<color=green>YES</color>" : "<color=red>NO</color>")}");
        Debug.Log("=====================================");
    }

    [Button("Test Sprite Generation", ButtonSizes.Medium)]
    [ShowIf("@useSpriteBasedSystem && vegetationType != null")]
    void TestSpriteGeneration()
    {
        if (!useSpriteBasedSystem || vegetationType == null)
        {
            Debug.LogWarning("Cannot test sprite generation - sprite-based system not properly configured!");
            return;
        }

        Debug.Log($"=== Testing Sprite Generation for {vegetationName} ===");

        for (int i = 0; i < 5; i++)
        {
            Sprite testSprite = GetRandomSprite();
            Debug.Log($"Test {i + 1}: {(testSprite != null ? testSprite.name : "NULL")}");
        }

        Debug.Log("=====================================");
    }
    #endif
}