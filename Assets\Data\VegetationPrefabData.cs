using UnityEngine;
using Sirenix.OdinInspector;

[CreateAssetMenu(fileName = "VegetationPrefab", menuName = "2D Rogue/Vegetation Prefab Data")]
public class VegetationPrefabData : ScriptableObject
{
    [Title("Vegetation Prefab Settings")]
    [FoldoutGroup("General")]
    [SerializeField]
    private string vegetationName = "New Vegetation";
    
    [FoldoutGroup("General")]
    [SerializeField, TextArea(2, 4)]
    private string description;
    
    [FoldoutGroup("General")]
    [SerializeField]
    [AssetsOnly]
    [Required("Prefab is required")]
    private GameObject prefab;
    
    [Title("Spawning Settings")]
    [FoldoutGroup("Spawning")]
    [SerializeField, Range(0f, 1f)]
    [Tooltip("Base chance to spawn this vegetation type")]
    private float spawnChance = 0.3f;
    
    [FoldoutGroup("Spawning")]
    [SerializeField]
    [Tooltip("Minimum distance between instances of this vegetation")]
    private float minimumSpacing = 0.5f;
    
    [FoldoutGroup("Spawning")]
    [SerializeField]
    [Tooltip("Can spawn on tile edges or only centers")]
    private bool allowEdgePlacement = true;
    
    [Title("Visual Variation")]
    [FoldoutGroup("Variation")]
    [SerializeField]
    [MinMaxSlider(0.5f, 2f, true)]
    [Tooltip("Random scale range for visual variety")]
    private Vector2 scaleRange = new Vector2(0.9f, 1.1f);
    
    [FoldoutGroup("Variation")]
    [SerializeField]
    [Tooltip("Random rotation on spawn")]
    private bool randomRotation = false;
    
    [FoldoutGroup("Variation")]
    [SerializeField]
    [ShowIf("randomRotation")]
    [MinMaxSlider(0f, 360f, true)]
    private Vector2 rotationRange = new Vector2(0f, 360f);
    
    [FoldoutGroup("Variation")]
    [SerializeField]
    [Tooltip("Random sprite flip for variety")]
    private bool allowRandomFlip = true;
    
    [Title("Y-Sorting")]
    [FoldoutGroup("Sorting")]
    [SerializeField]
    [Tooltip("Y-offset for sorting (negative values sort behind player)")]
    private float ySortingOffset = 0f;
    
    [FoldoutGroup("Sorting")]
    [SerializeField]
    [Tooltip("Base sorting order offset")]
    private int sortingOrderOffset = 0;
    
    [Title("Performance")]
    [FoldoutGroup("Performance")]
    [SerializeField]
    [Tooltip("Maximum instances per chunk")]
    private int maxPerChunk = 50;
    
    [FoldoutGroup("Performance")]
    [SerializeField]
    [Tooltip("Initial pool size for this vegetation type")]
    private int initialPoolSize = 100;
    
    [FoldoutGroup("Performance")]
    [SerializeField]
    [Tooltip("Pool grow size when exhausted")]
    private int poolGrowSize = 20;
    
    // Properties
    public string VegetationName => vegetationName;
    public GameObject Prefab => prefab;
    public float SpawnChance => spawnChance;
    public float MinimumSpacing => minimumSpacing;
    public bool AllowEdgePlacement => allowEdgePlacement;
    public Vector2 ScaleRange => scaleRange;
    public bool RandomRotation => randomRotation;
    public Vector2 RotationRange => rotationRange;
    public bool AllowRandomFlip => allowRandomFlip;
    public float YSortingOffset => ySortingOffset;
    public int SortingOrderOffset => sortingOrderOffset;
    public int MaxPerChunk => maxPerChunk;
    public int InitialPoolSize => initialPoolSize;
    public int PoolGrowSize => poolGrowSize;
    
    public float GetRandomScale()
    {
        return Random.Range(scaleRange.x, scaleRange.y);
    }
    
    public Quaternion GetRandomRotation()
    {
        if (!randomRotation) return Quaternion.identity;
        
        float angle = Random.Range(rotationRange.x, rotationRange.y);
        return Quaternion.Euler(0f, 0f, angle);
    }
    
    public bool GetRandomFlip()
    {
        return allowRandomFlip && Random.value > 0.5f;
    }
    
    #if UNITY_EDITOR
    [Button("Validate Prefab", ButtonSizes.Medium)]
    void ValidatePrefab()
    {
        if (prefab == null)
        {
            Debug.LogError($"No prefab assigned to {vegetationName}!");
            return;
        }
        
        bool hasRenderer = prefab.GetComponent<SpriteRenderer>() != null;
        bool hasYSorting = prefab.GetComponent<YSortingController>() != null;
        
        Debug.Log($"=== Vegetation Prefab Validation: {vegetationName} ===");
        Debug.Log($"Prefab: {prefab.name}");
        Debug.Log($"Has SpriteRenderer: {(hasRenderer ? "<color=green>YES</color>" : "<color=red>NO</color>")}");
        Debug.Log($"Has YSortingController: {(hasYSorting ? "<color=green>YES</color>" : "<color=yellow>NO (will be added at runtime)</color>")}");
        Debug.Log($"Initial Pool Size: {initialPoolSize}");
        Debug.Log($"Max Per Chunk: {maxPerChunk}");
        Debug.Log("=====================================");
    }
    #endif
}