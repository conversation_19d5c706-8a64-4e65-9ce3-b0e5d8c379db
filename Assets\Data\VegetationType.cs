using UnityEngine;
using Sirenix.OdinInspector;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Defines a type of vegetation with its associated sprites and properties.
/// This allows for flexible sprite-based vegetation spawning from spritesheets.
/// </summary>
[CreateAssetMenu(fileName = "VegetationType", menuName = "2D Rogue/Vegetation Type")]
public class VegetationType : ScriptableObject
{
    [Title("Vegetation Type Settings")]
    [FoldoutGroup("General")]
    [SerializeField]
    private string typeName = "New Vegetation Type";
    
    [FoldoutGroup("General")]
    [SerializeField, TextArea(2, 4)]
    private string description;
    
    [FoldoutGroup("General")]
    [SerializeField]
    [Tooltip("Category for organization (e.g., 'Grass', 'Bushes', 'Flowers')")]
    private string category = "Grass";
    
    [Title("Sprite Configuration")]
    [FoldoutGroup("Sprites")]
    [SerializeField]
    [Required("At least one sprite variant is required")]
    [ListDrawerSettings(
        DraggableItems = true,
        ShowIndexLabels = true,
        NumberOfItemsPerPage = 10
    )]
    [Tooltip("Array of sprite variants for this vegetation type")]
    private Sprite[] spriteVariants = new Sprite[0];
    
    [FoldoutGroup("Sprites")]
    [SerializeField]
    [Range(0f, 1f)]
    [Tooltip("Probability weights for sprite selection (if empty, uses uniform distribution)")]
    private float[] spriteWeights = new float[0];
    
    [Title("Animation Support")]
    [FoldoutGroup("Animation")]
    [SerializeField]
    [Tooltip("Optional sprite animation for this vegetation type")]
    private SpriteAnimation idleAnimation;
    
    [FoldoutGroup("Animation")]
    [SerializeField]
    [Tooltip("Optional wind animation for enhanced sway effects")]
    private SpriteAnimation windAnimation;
    
    [Title("Visual Properties")]
    [FoldoutGroup("Visual")]
    [SerializeField]
    [Tooltip("Default sorting layer for this vegetation type")]
    private string sortingLayerName = "Default";
    
    [FoldoutGroup("Visual")]
    [SerializeField]
    [Tooltip("Base sorting order offset for this vegetation type")]
    private int baseSortingOrder = 0;
    
    [FoldoutGroup("Visual")]
    [SerializeField]
    [ColorUsage(false)]
    [Tooltip("Optional color tint for this vegetation type")]
    private Color tintColor = Color.white;
    
    [FoldoutGroup("Visual")]
    [SerializeField]
    [Range(0f, 2f)]
    [Tooltip("Scale multiplier for this vegetation type")]
    private float scaleMultiplier = 1f;
    
    // Cached data for performance
    private float[] normalizedWeights;
    private bool weightsNormalized = false;
    
    // Properties
    public string TypeName => typeName;
    public string Description => description;
    public string Category => category;
    public Sprite[] SpriteVariants => spriteVariants;
    public SpriteAnimation IdleAnimation => idleAnimation;
    public SpriteAnimation WindAnimation => windAnimation;
    public string SortingLayerName => sortingLayerName;
    public int BaseSortingOrder => baseSortingOrder;
    public Color TintColor => tintColor;
    public float ScaleMultiplier => scaleMultiplier;
    
    /// <summary>
    /// Gets a random sprite variant based on weights or uniform distribution
    /// </summary>
    public Sprite GetRandomSprite()
    {
        if (spriteVariants == null || spriteVariants.Length == 0)
        {
            Debug.LogWarning($"VegetationType '{typeName}' has no sprite variants!");
            return null;
        }
        
        if (spriteVariants.Length == 1)
        {
            return spriteVariants[0];
        }
        
        // Use weighted selection if weights are provided
        if (spriteWeights != null && spriteWeights.Length == spriteVariants.Length)
        {
            return GetWeightedRandomSprite();
        }
        
        // Otherwise use uniform distribution
        int randomIndex = Random.Range(0, spriteVariants.Length);
        return spriteVariants[randomIndex];
    }
    
    /// <summary>
    /// Gets a specific sprite variant by index
    /// </summary>
    public Sprite GetSpriteVariant(int index)
    {
        if (spriteVariants == null || index < 0 || index >= spriteVariants.Length)
        {
            Debug.LogWarning($"Invalid sprite variant index {index} for VegetationType '{typeName}'");
            return null;
        }
        
        return spriteVariants[index];
    }
    
    /// <summary>
    /// Gets the number of available sprite variants
    /// </summary>
    public int GetVariantCount()
    {
        return spriteVariants?.Length ?? 0;
    }
    
    /// <summary>
    /// Validates that the vegetation type is properly configured
    /// </summary>
    public bool IsValid()
    {
        if (spriteVariants == null || spriteVariants.Length == 0)
            return false;
            
        // Check for null sprites
        foreach (var sprite in spriteVariants)
        {
            if (sprite == null)
                return false;
        }
        
        // Validate weights if provided
        if (spriteWeights != null && spriteWeights.Length > 0)
        {
            if (spriteWeights.Length != spriteVariants.Length)
                return false;
                
            float totalWeight = 0f;
            foreach (var weight in spriteWeights)
            {
                if (weight < 0f)
                    return false;
                totalWeight += weight;
            }
            
            if (totalWeight <= 0f)
                return false;
        }
        
        return true;
    }
    
    private Sprite GetWeightedRandomSprite()
    {
        if (!weightsNormalized)
        {
            NormalizeWeights();
        }
        
        float randomValue = Random.value;
        float cumulativeWeight = 0f;
        
        for (int i = 0; i < normalizedWeights.Length; i++)
        {
            cumulativeWeight += normalizedWeights[i];
            if (randomValue <= cumulativeWeight)
            {
                return spriteVariants[i];
            }
        }
        
        // Fallback to last sprite
        return spriteVariants[spriteVariants.Length - 1];
    }
    
    private void NormalizeWeights()
    {
        if (spriteWeights == null || spriteWeights.Length == 0)
            return;
            
        float totalWeight = 0f;
        foreach (var weight in spriteWeights)
        {
            totalWeight += weight;
        }
        
        if (totalWeight <= 0f)
            return;
            
        normalizedWeights = new float[spriteWeights.Length];
        for (int i = 0; i < spriteWeights.Length; i++)
        {
            normalizedWeights[i] = spriteWeights[i] / totalWeight;
        }
        
        weightsNormalized = true;
    }
    
    private void OnValidate()
    {
        // Reset normalization when weights change
        weightsNormalized = false;
        
        // Ensure weights array matches sprites array if weights are used
        if (spriteWeights != null && spriteWeights.Length > 0 && 
            spriteVariants != null && spriteWeights.Length != spriteVariants.Length)
        {
            System.Array.Resize(ref spriteWeights, spriteVariants.Length);
        }
    }
    
    #if UNITY_EDITOR
    [Button("Validate Configuration", ButtonSizes.Medium)]
    [FoldoutGroup("Debug")]
    private void ValidateConfiguration()
    {
        if (IsValid())
        {
            Debug.Log($"VegetationType '{typeName}' is valid with {GetVariantCount()} sprite variants.");
        }
        else
        {
            Debug.LogError($"VegetationType '{typeName}' has configuration issues!");
        }
    }
    
    [Button("Import Sprites from Texture", ButtonSizes.Large)]
    [FoldoutGroup("Debug")]
    [InfoBox("Drag a sliced sprite sheet here to import all sub-sprites as variants")]
    private void ImportSpritesFromTexture(Texture2D spriteSheet)
    {
        if (spriteSheet == null) return;
        
        string path = UnityEditor.AssetDatabase.GetAssetPath(spriteSheet);
        Sprite[] sprites = UnityEditor.AssetDatabase.LoadAllAssetsAtPath(path)
            .OfType<Sprite>()
            .OrderBy(s => s.name)
            .ToArray();
        
        if (sprites.Length > 0)
        {
            spriteVariants = sprites;
            
            // Reset weights to uniform distribution
            spriteWeights = new float[sprites.Length];
            for (int i = 0; i < spriteWeights.Length; i++)
            {
                spriteWeights[i] = 1f;
            }
            
            UnityEditor.EditorUtility.SetDirty(this);
            Debug.Log($"Imported {sprites.Length} sprite variants from {spriteSheet.name}");
        }
    }
    #endif
}
