#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using Sirenix.OdinInspector;

public class VegetationPrefabCreator : ScriptableObject
{
    [Title("Vegetation Prefab Creator")]
    [InfoBox("This tool helps create vegetation prefabs with all required components for the Y-sorting system.")]
    
    [SerializeField]
    [Required]
    private Sprite vegetationSprite;
    
    [SerializeField]
    private string prefabName = "NewVegetation";
    
    [SerializeField]
    [FolderPath]
    private string savePath = "Assets/Prefabs/Vegetation";
    
    [SerializeField]
    private bool addVegetationInstance = true;
    
    [SerializeField]
    [ShowIf("addVegetationInstance")]
    private bool enableSway = false;
    
    [Button("Create Vegetation Prefab", ButtonSizes.Large)]
    void CreatePrefab()
    {
        if (vegetationSprite == null)
        {
            Debug.LogError("Please assign a sprite first!");
            return;
        }
        
        // Create GameObject
        GameObject vegetationObj = new GameObject(prefabName);
        
        // Add SpriteRenderer
        SpriteRenderer spriteRenderer = vegetationObj.AddComponent<SpriteRenderer>();
        spriteRenderer.sprite = vegetationSprite;
        spriteRenderer.sortingLayerName = "Default";
        
        // Add YSortingController
        YSortingController ySorting = vegetationObj.AddComponent<YSortingController>();
        
        // Set Y offset based on sprite bounds
        if (vegetationSprite != null)
        {
            float spriteBottom = vegetationSprite.bounds.min.y;
            ySorting.SetYOffset(spriteBottom);
        }
        
        // Add VegetationInstance if requested
        if (addVegetationInstance)
        {
            VegetationInstance instance = vegetationObj.AddComponent<VegetationInstance>();
            // Set sway via reflection since it's private
            var swayField = typeof(VegetationInstance).GetField("enableSway", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (swayField != null)
            {
                swayField.SetValue(instance, enableSway);
            }
        }
        
        // Ensure save path exists
        if (!AssetDatabase.IsValidFolder(savePath))
        {
            System.IO.Directory.CreateDirectory(savePath);
            AssetDatabase.Refresh();
        }
        
        // Save as prefab
        string fullPath = $"{savePath}/{prefabName}.prefab";
        
        GameObject prefab = PrefabUtility.SaveAsPrefabAsset(vegetationObj, fullPath);
        
        // Clean up scene object
        DestroyImmediate(vegetationObj);
        
        // Select the created prefab
        Selection.activeObject = prefab;
        EditorGUIUtility.PingObject(prefab);
        
        Debug.Log($"<color=green>✓ Created vegetation prefab: {fullPath}</color>");
        
        // Offer to create VegetationPrefabData
        if (EditorUtility.DisplayDialog("Create VegetationPrefabData?", 
            "Would you like to create a VegetationPrefabData ScriptableObject for this prefab?", 
            "Yes", "No"))
        {
            CreateVegetationData(prefab);
        }
    }
    
    void CreateVegetationData(GameObject prefab)
    {
        VegetationPrefabData data = CreateInstance<VegetationPrefabData>();
        
        // Set prefab reference via reflection
        var prefabField = typeof(VegetationPrefabData).GetField("prefab", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (prefabField != null)
        {
            prefabField.SetValue(data, prefab);
        }
        
        var nameField = typeof(VegetationPrefabData).GetField("vegetationName", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (nameField != null)
        {
            nameField.SetValue(data, prefabName);
        }
        
        string dataPath = $"{savePath}/{prefabName}_Data.asset";
        AssetDatabase.CreateAsset(data, dataPath);
        AssetDatabase.SaveAssets();
        
        Selection.activeObject = data;
        EditorGUIUtility.PingObject(data);
        
        Debug.Log($"<color=green>✓ Created VegetationPrefabData: {dataPath}</color>");
    }
    
    [MenuItem("Tools/2D Rogue/Vegetation Prefab Creator")]
    static void CreateWindow()
    {
        VegetationPrefabCreator creator = CreateInstance<VegetationPrefabCreator>();
        Selection.activeObject = creator;
        
        // Create in a temporary location
        string tempPath = "Assets/VegetationPrefabCreator_Temp.asset";
        AssetDatabase.CreateAsset(creator, tempPath);
        EditorGUIUtility.PingObject(creator);
        
        Debug.Log("Vegetation Prefab Creator ready. Configure settings and click 'Create Vegetation Prefab'.");
    }
}
#endif