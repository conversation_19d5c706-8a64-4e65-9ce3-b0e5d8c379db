using UnityEngine;
using UnityEngine.U2D;
using Sirenix.OdinInspector;
using System.Collections.Generic;

/// <summary>
/// Testing utility for the sprite-based vegetation system.
/// Provides validation and testing tools for vegetation components.
/// </summary>
public class VegetationSystemTester : MonoBehaviour
{
    [Title("Vegetation System Tester")]
    
    [FoldoutGroup("Test Data")]
    [SerializeField]
    private VegetationType[] testVegetationTypes;
    
    [FoldoutGroup("Test Data")]
    [SerializeField]
    private VegetationPrefabData[] testPrefabData;
    
    [FoldoutGroup("Test Data")]
    [SerializeField]
    private BiomeData[] testBiomes;
    
    [FoldoutGroup("Test Data")]
    [SerializeField]
    private SpriteAtlas[] testAtlases;
    
    [FoldoutGroup("Test Data")]
    [SerializeField]
    private GameObject genericVegetationPrefab;
    
    [Title("Test Results")]
    [FoldoutGroup("Results")]
    [SerializeField, ReadOnly]
    private string lastTestResult = "No tests run";
    
    [Button("Test All Systems", ButtonSizes.Large)]
    [FoldoutGroup("Tests")]
    public void TestAllSystems()
    {
        string results = "=== Vegetation System Test Results ===\n\n";
        
        results += TestVegetationTypes();
        results += TestPrefabData();
        results += TestBiomeData();
        results += TestSpriteAtlasManager();
        results += TestVegetationInstance();
        
        results += "\n=== Test Complete ===";
        
        lastTestResult = results;
        Debug.Log(results);
    }
    
    [Button("Test VegetationType Assets", ButtonSizes.Medium)]
    [FoldoutGroup("Tests")]
    public void TestVegetationTypesOnly()
    {
        string results = TestVegetationTypes();
        lastTestResult = results;
        Debug.Log(results);
    }
    
    [Button("Test Sprite Atlas Integration", ButtonSizes.Medium)]
    [FoldoutGroup("Tests")]
    public void TestSpriteAtlasOnly()
    {
        string results = TestSpriteAtlasManager();
        lastTestResult = results;
        Debug.Log(results);
    }
    
    private string TestVegetationTypes()
    {
        string results = "--- VegetationType Tests ---\n";
        
        if (testVegetationTypes == null || testVegetationTypes.Length == 0)
        {
            results += "❌ No VegetationType assets to test\n\n";
            return results;
        }
        
        int validCount = 0;
        foreach (var vegType in testVegetationTypes)
        {
            if (vegType == null)
            {
                results += "❌ Null VegetationType in test array\n";
                continue;
            }
            
            results += $"Testing: {vegType.TypeName}\n";
            
            if (vegType.IsValid())
            {
                results += $"  ✅ Valid configuration\n";
                results += $"  📊 {vegType.GetVariantCount()} sprite variants\n";
                
                // Test sprite generation
                for (int i = 0; i < 3; i++)
                {
                    Sprite testSprite = vegType.GetRandomSprite();
                    results += $"  🎲 Random sprite {i + 1}: {(testSprite != null ? testSprite.name : "NULL")}\n";
                }
                
                validCount++;
            }
            else
            {
                results += $"  ❌ Invalid configuration\n";
            }
        }
        
        results += $"\nSummary: {validCount}/{testVegetationTypes.Length} VegetationType assets valid\n\n";
        return results;
    }
    
    private string TestPrefabData()
    {
        string results = "--- VegetationPrefabData Tests ---\n";
        
        if (testPrefabData == null || testPrefabData.Length == 0)
        {
            results += "❌ No VegetationPrefabData assets to test\n\n";
            return results;
        }
        
        int validCount = 0;
        int spriteBasedCount = 0;
        
        foreach (var prefabData in testPrefabData)
        {
            if (prefabData == null)
            {
                results += "❌ Null VegetationPrefabData in test array\n";
                continue;
            }
            
            results += $"Testing: {prefabData.VegetationName}\n";
            
            if (prefabData.IsValidConfiguration())
            {
                results += $"  ✅ Valid configuration\n";
                results += $"  🔧 System: {(prefabData.UseSpriteBasedSystem ? "Sprite-based" : "Traditional")}\n";
                
                if (prefabData.UseSpriteBasedSystem)
                {
                    spriteBasedCount++;
                    results += $"  🎨 Scale Multiplier: {prefabData.GetEffectiveScaleMultiplier():F2}\n";
                    results += $"  🌈 Tint Color: {prefabData.GetTintColor()}\n";
                    
                    // Test sprite generation
                    Sprite testSprite = prefabData.GetRandomSprite();
                    results += $"  🎲 Test sprite: {(testSprite != null ? testSprite.name : "NULL")}\n";
                }
                
                validCount++;
            }
            else
            {
                results += $"  ❌ Invalid configuration\n";
            }
        }
        
        results += $"\nSummary: {validCount}/{testPrefabData.Length} PrefabData assets valid\n";
        results += $"Sprite-based: {spriteBasedCount}, Traditional: {validCount - spriteBasedCount}\n\n";
        return results;
    }
    
    private string TestBiomeData()
    {
        string results = "--- BiomeData Tests ---\n";
        
        if (testBiomes == null || testBiomes.Length == 0)
        {
            results += "❌ No BiomeData assets to test\n\n";
            return results;
        }
        
        int validCount = 0;
        int spriteBasedCount = 0;
        
        foreach (var biome in testBiomes)
        {
            if (biome == null)
            {
                results += "❌ Null BiomeData in test array\n";
                continue;
            }
            
            results += $"Testing: {biome.BiomeName}\n";
            
            bool hasVegetation = biome.HasAnyVegetation();
            results += $"  🌱 Has vegetation: {hasVegetation}\n";
            
            if (hasVegetation)
            {
                results += $"  🔧 Traditional system: {biome.HasPrefabVegetation()}\n";
                results += $"  🎨 Sprite-based system: {biome.HasSpriteBasedVegetation()}\n";
                
                if (biome.UseSpriteBasedVegetation)
                {
                    spriteBasedCount++;
                    
                    // Test vegetation type generation
                    for (int i = 0; i < 3; i++)
                    {
                        VegetationType testType = biome.GetRandomVegetationType(0.5f);
                        results += $"  🎲 Random type {i + 1}: {(testType != null ? testType.TypeName : "NULL")}\n";
                    }
                }
                
                validCount++;
            }
        }
        
        results += $"\nSummary: {validCount}/{testBiomes.Length} BiomeData assets have vegetation\n";
        results += $"Sprite-based: {spriteBasedCount}, Traditional: {validCount - spriteBasedCount}\n\n";
        return results;
    }
    
    private string TestSpriteAtlasManager()
    {
        string results = "--- SpriteAtlasManager Tests ---\n";
        
        if (SpriteAtlasManager.Instance == null)
        {
            results += "❌ SpriteAtlasManager instance not found\n\n";
            return results;
        }
        
        results += "✅ SpriteAtlasManager instance found\n";
        
        if (testAtlases != null && testAtlases.Length > 0)
        {
            foreach (var atlas in testAtlases)
            {
                if (atlas == null) continue;
                
                results += $"Testing atlas: {atlas.name}\n";
                
                // Cache the atlas
                SpriteAtlasManager.Instance.CacheAtlasSprites(atlas);
                
                // Get sprites
                Sprite[] sprites = SpriteAtlasManager.Instance.GetAtlasSprites(atlas);
                results += $"  📊 Cached sprites: {sprites.Length}\n";
                
                // Test pattern matching
                Sprite[] grassSprites = SpriteAtlasManager.Instance.GetSpritesWithPattern(atlas, "grass");
                results += $"  🌱 'grass' pattern matches: {grassSprites.Length}\n";
            }
        }
        else
        {
            results += "⚠️ No test atlases provided\n";
        }
        
        results += "\n";
        return results;
    }
    
    private string TestVegetationInstance()
    {
        string results = "--- VegetationInstance Tests ---\n";
        
        if (genericVegetationPrefab == null)
        {
            results += "❌ No generic vegetation prefab provided\n\n";
            return results;
        }
        
        VegetationInstance instance = genericVegetationPrefab.GetComponent<VegetationInstance>();
        if (instance == null)
        {
            results += "❌ Generic prefab missing VegetationInstance component\n\n";
            return results;
        }
        
        results += "✅ VegetationInstance component found\n";
        
        SpriteRenderer spriteRenderer = genericVegetationPrefab.GetComponent<SpriteRenderer>();
        results += $"🖼️ SpriteRenderer: {(spriteRenderer != null ? "Found" : "Missing")}\n";
        
        SpriteAnimator spriteAnimator = genericVegetationPrefab.GetComponent<SpriteAnimator>();
        results += $"🎬 SpriteAnimator: {(spriteAnimator != null ? "Found" : "Missing")}\n";
        
        results += "\n";
        return results;
    }
    
    [Button("Clear Test Results", ButtonSizes.Small)]
    [FoldoutGroup("Results")]
    private void ClearResults()
    {
        lastTestResult = "Results cleared";
    }
}
