using UnityEngine;
using UnityEngine.U2D;
using System.Collections.Generic;
using System.Linq;
using Sirenix.OdinInspector;

/// <summary>
/// Manages sprite atlas loading and caching for efficient sprite access.
/// Provides utilities for working with Unity sprite atlases in the vegetation system.
/// </summary>
public class SpriteAtlasManager : MonoBehaviour
{
    [Title("Sprite Atlas Manager")]
    [SerializeField]
    [Tooltip("Sprite atlases to preload and cache")]
    private SpriteAtlas[] preloadAtlases = new SpriteAtlas[0];
    
    [SerializeField]
    [Tooltip("Enable debug logging")]
    private bool debugMode = false;
    
    // Singleton instance
    public static SpriteAtlasManager Instance { get; private set; }
    
    // Cached sprite data
    private Dictionary<SpriteAtlas, Sprite[]> atlasSpritesCache = new Dictionary<SpriteAtlas, Sprite[]>();
    private Dictionary<string, Sprite> spriteNameCache = new Dictionary<string, Sprite>();
    private Dictionary<SpriteAtlas, Dictionary<string, Sprite>> atlasNameLookup = new Dictionary<SpriteAtlas, Dictionary<string, Sprite>>();
    
    void Awake()
    {
        // Singleton setup
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeAtlases();
        }
        else if (Instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    private void InitializeAtlases()
    {
        if (preloadAtlases == null || preloadAtlases.Length == 0)
        {
            if (debugMode)
                Debug.Log("SpriteAtlasManager: No atlases to preload");
            return;
        }
        
        foreach (var atlas in preloadAtlases)
        {
            if (atlas != null)
            {
                CacheAtlasSprites(atlas);
            }
        }
        
        if (debugMode)
            Debug.Log($"SpriteAtlasManager: Initialized with {preloadAtlases.Length} atlases, cached {spriteNameCache.Count} sprites");
    }
    
    /// <summary>
    /// Caches all sprites from a sprite atlas for fast lookup
    /// </summary>
    public void CacheAtlasSprites(SpriteAtlas atlas)
    {
        if (atlas == null)
        {
            Debug.LogWarning("SpriteAtlasManager: Attempted to cache null atlas");
            return;
        }
        
        if (atlasSpritesCache.ContainsKey(atlas))
        {
            if (debugMode)
                Debug.Log($"SpriteAtlasManager: Atlas '{atlas.name}' already cached");
            return;
        }
        
        // Get all sprites from the atlas
        Sprite[] sprites = new Sprite[atlas.spriteCount];
        atlas.GetSprites(sprites);
        
        // Cache the sprite array
        atlasSpritesCache[atlas] = sprites;
        
        // Create name lookup for this atlas
        Dictionary<string, Sprite> nameLookup = new Dictionary<string, Sprite>();
        
        foreach (var sprite in sprites)
        {
            if (sprite != null)
            {
                // Add to global name cache (last atlas wins for duplicate names)
                spriteNameCache[sprite.name] = sprite;
                
                // Add to atlas-specific lookup
                nameLookup[sprite.name] = sprite;
            }
        }
        
        atlasNameLookup[atlas] = nameLookup;
        
        if (debugMode)
            Debug.Log($"SpriteAtlasManager: Cached {sprites.Length} sprites from atlas '{atlas.name}'");
    }
    
    /// <summary>
    /// Gets a sprite by name from any cached atlas
    /// </summary>
    public Sprite GetSprite(string spriteName)
    {
        if (string.IsNullOrEmpty(spriteName))
            return null;
            
        if (spriteNameCache.TryGetValue(spriteName, out Sprite sprite))
        {
            return sprite;
        }
        
        if (debugMode)
            Debug.LogWarning($"SpriteAtlasManager: Sprite '{spriteName}' not found in cache");
            
        return null;
    }
    
    /// <summary>
    /// Gets a sprite by name from a specific atlas
    /// </summary>
    public Sprite GetSpriteFromAtlas(SpriteAtlas atlas, string spriteName)
    {
        if (atlas == null || string.IsNullOrEmpty(spriteName))
            return null;
            
        // Ensure atlas is cached
        if (!atlasNameLookup.ContainsKey(atlas))
        {
            CacheAtlasSprites(atlas);
        }
        
        if (atlasNameLookup.TryGetValue(atlas, out Dictionary<string, Sprite> lookup))
        {
            if (lookup.TryGetValue(spriteName, out Sprite sprite))
            {
                return sprite;
            }
        }
        
        if (debugMode)
            Debug.LogWarning($"SpriteAtlasManager: Sprite '{spriteName}' not found in atlas '{atlas.name}'");
            
        return null;
    }
    
    /// <summary>
    /// Gets all sprites from a cached atlas
    /// </summary>
    public Sprite[] GetAtlasSprites(SpriteAtlas atlas)
    {
        if (atlas == null)
            return new Sprite[0];
            
        if (!atlasSpritesCache.ContainsKey(atlas))
        {
            CacheAtlasSprites(atlas);
        }
        
        return atlasSpritesCache.TryGetValue(atlas, out Sprite[] sprites) ? sprites : new Sprite[0];
    }
    
    /// <summary>
    /// Gets sprites from an atlas that match a name pattern
    /// </summary>
    public Sprite[] GetSpritesWithPattern(SpriteAtlas atlas, string namePattern)
    {
        if (atlas == null || string.IsNullOrEmpty(namePattern))
            return new Sprite[0];
            
        Sprite[] allSprites = GetAtlasSprites(atlas);
        return allSprites.Where(s => s != null && s.name.Contains(namePattern)).ToArray();
    }
    
    /// <summary>
    /// Gets sprites from an atlas that start with a specific prefix
    /// </summary>
    public Sprite[] GetSpritesWithPrefix(SpriteAtlas atlas, string prefix)
    {
        if (atlas == null || string.IsNullOrEmpty(prefix))
            return new Sprite[0];
            
        Sprite[] allSprites = GetAtlasSprites(atlas);
        return allSprites.Where(s => s != null && s.name.StartsWith(prefix)).ToArray();
    }
    
    /// <summary>
    /// Clears all cached data
    /// </summary>
    public void ClearCache()
    {
        atlasSpritesCache.Clear();
        spriteNameCache.Clear();
        atlasNameLookup.Clear();
        
        if (debugMode)
            Debug.Log("SpriteAtlasManager: Cache cleared");
    }
    
    /// <summary>
    /// Gets cache statistics
    /// </summary>
    public void LogCacheStats()
    {
        Debug.Log($"=== SpriteAtlasManager Cache Statistics ===");
        Debug.Log($"Cached Atlases: {atlasSpritesCache.Count}");
        Debug.Log($"Total Cached Sprites: {spriteNameCache.Count}");
        
        foreach (var kvp in atlasSpritesCache)
        {
            if (kvp.Key != null)
            {
                Debug.Log($"  - {kvp.Key.name}: {kvp.Value.Length} sprites");
            }
        }
        
        Debug.Log("==========================================");
    }
    
    #if UNITY_EDITOR
    [Button("Cache All Preload Atlases", ButtonSizes.Medium)]
    [FoldoutGroup("Debug")]
    private void CacheAllPreloadAtlases()
    {
        ClearCache();
        InitializeAtlases();
    }
    
    [Button("Log Cache Statistics", ButtonSizes.Medium)]
    [FoldoutGroup("Debug")]
    private void LogStats()
    {
        LogCacheStats();
    }
    
    [Button("Clear Cache", ButtonSizes.Medium)]
    [FoldoutGroup("Debug")]
    private void ClearCacheButton()
    {
        ClearCache();
    }
    #endif
}
