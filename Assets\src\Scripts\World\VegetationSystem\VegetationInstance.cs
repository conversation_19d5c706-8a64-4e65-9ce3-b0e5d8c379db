using UnityEngine;
using Sirenix.OdinInspector;

[RequireComponent(typeof(SpriteRenderer))]
public class VegetationInstance : MonoBehaviour, ISpawnable
{
    [Title("Vegetation Instance")]
    [SerializeField]
    [Tooltip("Optional animation/sway settings")]
    private bool enableSway = false;
    
    [SerializeField]
    [ShowIf("enableSway")]
    [Range(0.1f, 2f)]
    private float swaySpeed = 1f;
    
    [SerializeField]
    [ShowIf("enableSway")]
    [Range(0f, 10f)]
    private float swayAmount = 2f;
    
    private SpriteRenderer spriteRenderer;
    private SpriteAnimator spriteAnimator;
    private float swayOffset;
    private Vector3 originalRotation;

    // Sprite-based system data
    private VegetationPrefabData currentPrefabData;
    private Sprite originalSprite;
    private Color originalColor;
    private Vector3 originalScale;
    private string originalSortingLayer;
    private int originalSortingOrder;
    
    void Awake()
    {
        spriteRenderer = GetComponent<SpriteRenderer>();
        spriteAnimator = GetComponent<SpriteAnimator>();

        // Store original values for reset
        if (spriteRenderer != null)
        {
            originalSprite = spriteRenderer.sprite;
            originalColor = spriteRenderer.color;
            originalSortingLayer = spriteRenderer.sortingLayerName;
            originalSortingOrder = spriteRenderer.sortingOrder;
        }

        originalScale = transform.localScale;
    }
    
    public void OnSpawn()
    {
        // Reset any modifications
        if (spriteRenderer != null)
        {
            spriteRenderer.flipX = false;
        }

        // Initialize sway with random offset
        if (enableSway)
        {
            swayOffset = Random.Range(0f, 2f * Mathf.PI);
            originalRotation = transform.eulerAngles;
        }
    }

    /// <summary>
    /// Configures this vegetation instance with sprite-based data
    /// </summary>
    public void ConfigureWithPrefabData(VegetationPrefabData prefabData)
    {
        if (prefabData == null)
        {
            Debug.LogWarning("VegetationInstance: Attempted to configure with null prefab data!");
            return;
        }

        currentPrefabData = prefabData;

        if (prefabData.UseSpriteBasedSystem)
        {
            ConfigureSpriteBasedSystem(prefabData);
        }
        else
        {
            ConfigureTraditionalSystem(prefabData);
        }
    }

    private void ConfigureSpriteBasedSystem(VegetationPrefabData prefabData)
    {
        if (spriteRenderer == null) return;

        // Set random sprite from vegetation type
        Sprite randomSprite = prefabData.GetRandomSprite();
        if (randomSprite != null)
        {
            spriteRenderer.sprite = randomSprite;
        }

        // Apply tint color
        Color tintColor = prefabData.GetTintColor();
        spriteRenderer.color = tintColor;

        // Apply sorting settings
        spriteRenderer.sortingLayerName = prefabData.GetSortingLayerName();
        spriteRenderer.sortingOrder = prefabData.GetEffectiveSortingOrder();

        // Apply scale multiplier
        float scaleMultiplier = prefabData.GetEffectiveScaleMultiplier();
        transform.localScale = originalScale * scaleMultiplier;

        // Setup animations if available and SpriteAnimator is present
        if (spriteAnimator != null)
        {
            SpriteAnimation idleAnimation = prefabData.GetIdleAnimation();
            if (idleAnimation != null)
            {
                spriteAnimator.Play(idleAnimation);
            }
        }
    }

    private void ConfigureTraditionalSystem(VegetationPrefabData prefabData)
    {
        // Traditional system doesn't need special configuration
        // The prefab already has the correct sprite set up

        // Apply any sorting order offset
        if (spriteRenderer != null)
        {
            spriteRenderer.sortingOrder = originalSortingOrder + prefabData.SortingOrderOffset;
        }
    }
    
    public void OnDespawn()
    {
        // Reset state before returning to pool
        ResetToOriginalState();

        // Clear current prefab data reference
        currentPrefabData = null;
    }

    /// <summary>
    /// Resets the vegetation instance to its original prefab state
    /// </summary>
    private void ResetToOriginalState()
    {
        transform.localScale = originalScale;
        transform.rotation = Quaternion.identity;

        if (spriteRenderer != null)
        {
            spriteRenderer.flipX = false;
            spriteRenderer.sprite = originalSprite;
            spriteRenderer.color = originalColor;
            spriteRenderer.sortingLayerName = originalSortingLayer;
            spriteRenderer.sortingOrder = originalSortingOrder;
        }

        // Stop any animations
        if (spriteAnimator != null)
        {
            spriteAnimator.Stop();
        }
    }

    /// <summary>
    /// Gets the current vegetation type (sprite-based system only)
    /// </summary>
    public VegetationType GetCurrentVegetationType()
    {
        return currentPrefabData?.VegetationType;
    }

    /// <summary>
    /// Gets the current prefab data
    /// </summary>
    public VegetationPrefabData GetCurrentPrefabData()
    {
        return currentPrefabData;
    }

    /// <summary>
    /// Checks if this instance is using the sprite-based system
    /// </summary>
    public bool IsUsingSpriteBasedSystem()
    {
        return currentPrefabData != null && currentPrefabData.UseSpriteBasedSystem;
    }

    /// <summary>
    /// Manually triggers wind animation if available (sprite-based system only)
    /// </summary>
    public void TriggerWindAnimation()
    {
        if (!IsUsingSpriteBasedSystem() || spriteAnimator == null)
            return;

        SpriteAnimation windAnimation = currentPrefabData.GetWindAnimation();
        if (windAnimation != null)
        {
            spriteAnimator.Play(windAnimation);
        }
    }

    /// <summary>
    /// Returns to idle animation if available (sprite-based system only)
    /// </summary>
    public void ReturnToIdleAnimation()
    {
        if (!IsUsingSpriteBasedSystem() || spriteAnimator == null)
            return;

        SpriteAnimation idleAnimation = currentPrefabData.GetIdleAnimation();
        if (idleAnimation != null)
        {
            spriteAnimator.Play(idleAnimation);
        }
    }
    
    void Update()
    {
        if (enableSway)
        {
            // Simple wind sway effect
            float sway = Mathf.Sin((Time.time * swaySpeed) + swayOffset) * swayAmount;
            transform.eulerAngles = originalRotation + new Vector3(0, 0, sway);
        }
    }
}