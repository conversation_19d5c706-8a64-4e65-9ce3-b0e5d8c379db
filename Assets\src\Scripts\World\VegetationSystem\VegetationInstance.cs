using UnityEngine;
using Sirenix.OdinInspector;

[RequireComponent(typeof(SpriteRenderer))]
public class VegetationInstance : MonoBehaviour, ISpawnable
{
    [Title("Vegetation Instance")]
    [SerializeField]
    [Tooltip("Optional animation/sway settings")]
    private bool enableSway = false;
    
    [SerializeField]
    [ShowIf("enableSway")]
    [Range(0.1f, 2f)]
    private float swaySpeed = 1f;
    
    [SerializeField]
    [ShowIf("enableSway")]
    [Range(0f, 10f)]
    private float swayAmount = 2f;
    
    private SpriteRenderer spriteRenderer;
    private float swayOffset;
    private Vector3 originalRotation;
    
    void Awake()
    {
        spriteRenderer = GetComponent<SpriteRenderer>();
    }
    
    public void OnSpawn()
    {
        // Reset any modifications
        if (spriteRenderer != null)
        {
            spriteRenderer.flipX = false;
        }
        
        // Initialize sway with random offset
        if (enableSway)
        {
            swayOffset = Random.Range(0f, 2f * Mathf.PI);
            originalRotation = transform.eulerAngles;
        }
    }
    
    public void OnDespawn()
    {
        // Reset state before returning to pool
        transform.localScale = Vector3.one;
        transform.rotation = Quaternion.identity;
        
        if (spriteRenderer != null)
        {
            spriteRenderer.flipX = false;
        }
    }
    
    void Update()
    {
        if (enableSway)
        {
            // Simple wind sway effect
            float sway = Mathf.Sin((Time.time * swaySpeed) + swayOffset) * swayAmount;
            transform.eulerAngles = originalRotation + new Vector3(0, 0, sway);
        }
    }
}