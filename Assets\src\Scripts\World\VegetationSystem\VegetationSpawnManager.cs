using UnityEngine;
using System.Collections.Generic;
using Sirenix.OdinInspector;

[RequireComponent(typeof(TilemapChunkManager))]
public class VegetationSpawnManager : MonoBehaviour
{
    [Title("Vegetation Spawn Manager")]
    [SerializeField]
    [Tooltip("Parent transform for all spawned vegetation")]
    private Transform vegetationParent;
    
    [SerializeField]
    [Tooltip("Enable debug logging")]
    private bool debugMode = false;
    
    // Singleton instance
    public static VegetationSpawnManager Instance { get; private set; }
    
    // References
    private TilemapChunkManager chunkManager;
    private BiomeManager biomeManager;
    
    // Tracking spawned vegetation per chunk
    private Dictionary<ChunkCoordinate, List<GameObject>> chunkVegetation = new Dictionary<ChunkCoordinate, List<GameObject>>();
    
    // Pool configuration tracking
    private HashSet<GameObject> configuredPools = new HashSet<GameObject>();
    
    // Cached collections to avoid allocations
    private List<ChunkCoordinate> chunksToRemove = new List<ChunkCoordinate>();
    private List<VegetationPlacement> placementCache = new List<VegetationPlacement>(256);
    
    // Struct for vegetation placement data
    private struct VegetationPlacement
    {
        public Vector3 position;
        public VegetationPrefabData prefabData;
        public float scale;
        public Quaternion rotation;
        public bool flipX;
    }
    
    void Awake()
    {
        if (Instance == null)
            Instance = this;
        else if (Instance != this)
            Destroy(gameObject);
            
        chunkManager = GetComponent<TilemapChunkManager>();
        
        if (vegetationParent == null)
        {
            GameObject parentObj = new GameObject("Vegetation");
            parentObj.transform.SetParent(transform);
            vegetationParent = parentObj.transform;
        }
    }
    
    void Start()
    {
        // Get biome manager reference
        biomeManager = chunkManager.GetBiomeManager();
            
        if (biomeManager == null)
        {
            Debug.LogError("VegetationSpawnManager: Could not access BiomeManager from TilemapChunkManager!");
        }
    }
    
    public void OnChunkGenerated(ChunkCoordinate coord, int seed)
    {
        if (debugMode)
            Debug.Log($"Generating vegetation for chunk {coord} with seed {seed}");
            
        GenerateVegetationForChunk(coord, seed);
    }
    
    public void OnChunkUnloaded(ChunkCoordinate coord)
    {
        if (debugMode)
            Debug.Log($"Unloading vegetation for chunk {coord}");
            
        DespawnVegetationForChunk(coord);
    }
    
    private void GenerateVegetationForChunk(ChunkCoordinate coord, int baseSeed)
    {
        // Use deterministic seed based on chunk coordinates and base seed
        int chunkSeed = baseSeed + coord.GetHashCode() + 3000; // Different offset than tiles
        Random.State originalState = Random.state;
        Random.InitState(chunkSeed);
        
        // Calculate chunk bounds
        int chunkWidth = chunkManager.GetChunkWidth();
        int chunkHeight = chunkManager.GetChunkHeight();
        Vector3 chunkWorldPos = coord.ToWorldPosition(chunkWidth, chunkHeight);
        
        // Clear placement cache
        placementCache.Clear();
        
        // Generate placement data first (deterministic)
        for (int x = 0; x < chunkWidth; x++)
        {
            for (int y = 0; y < chunkHeight; y++)
            {
                Vector3Int tilePos = new Vector3Int(
                    Mathf.FloorToInt(chunkWorldPos.x) + x,
                    Mathf.FloorToInt(chunkWorldPos.y) + y,
                    0
                );
                
                // Get biome for this position
                BiomeData biome = biomeManager.GetBiomeForWorldPosition(tilePos, chunkManager.GetChunkSize());
                
                if (biome != null && biome.HasPrefabVegetation())
                {
                    // Check if we should place vegetation here
                    if (ShouldPlaceVegetation(biome, tilePos))
                    {
                        VegetationPrefabData prefabData = biome.GetRandomVegetationPrefab(biome.HighGrassRatio);
                        
                        if (prefabData != null && prefabData.Prefab != null)
                        {
                            // Additional spawn chance from prefab data
                            if (Random.value <= prefabData.SpawnChance)
                            {
                                // Calculate position with optional edge placement
                                Vector3 position = new Vector3(tilePos.x + 0.5f, tilePos.y + 0.5f, 0);
                                if (prefabData.AllowEdgePlacement)
                                {
                                    position.x += Random.Range(-0.4f, 0.4f);
                                    position.y += Random.Range(-0.4f, 0.4f);
                                }
                                
                                // Check minimum spacing
                                if (CheckMinimumSpacing(position, prefabData.MinimumSpacing))
                                {
                                    VegetationPlacement placement = new VegetationPlacement
                                    {
                                        position = position,
                                        prefabData = prefabData,
                                        scale = prefabData.GetRandomScale(),
                                        rotation = prefabData.GetRandomRotation(),
                                        flipX = prefabData.GetRandomFlip()
                                    };
                                    
                                    placementCache.Add(placement);
                                    
                                    // Check max per chunk limit
                                    if (placementCache.Count >= prefabData.MaxPerChunk)
                                        break;
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // Restore random state
        Random.state = originalState;
        
        // Now spawn the vegetation using the pool manager
        SpawnVegetationFromPlacements(coord);
    }
    
    private bool ShouldPlaceVegetation(BiomeData biome, Vector3Int tilePos)
    {
        float vegetationChance = biome.VegetationDensity * biome.PrefabDensityMultiplier;
        
        // Apply noise-based clustering if enabled
        if (biome.UseVegetationClustering)
        {
            float noiseValue = Mathf.PerlinNoise(
                (tilePos.x * biome.VegetationNoiseScale) + biome.VegetationNoiseOffset.x,
                (tilePos.y * biome.VegetationNoiseScale) + biome.VegetationNoiseOffset.y
            );
            
            // Only place vegetation if noise is above threshold
            if (noiseValue < biome.VegetationNoiseThreshold)
            {
                return false;
            }
            
            // Modulate vegetation chance based on noise
            vegetationChance *= (noiseValue - biome.VegetationNoiseThreshold) / (1f - biome.VegetationNoiseThreshold);
        }
        
        return Random.value < vegetationChance;
    }
    
    private bool CheckMinimumSpacing(Vector3 position, float minimumSpacing)
    {
        if (minimumSpacing <= 0) return true;
        
        float spacingSqr = minimumSpacing * minimumSpacing;
        
        foreach (var placement in placementCache)
        {
            float distSqr = (placement.position - position).sqrMagnitude;
            if (distSqr < spacingSqr)
                return false;
        }
        
        return true;
    }
    
    private void SpawnVegetationFromPlacements(ChunkCoordinate coord)
    {
        if (placementCache.Count == 0) return;
        
        // Ensure we have a list for this chunk
        if (!chunkVegetation.ContainsKey(coord))
        {
            chunkVegetation[coord] = new List<GameObject>(placementCache.Count);
        }
        
        List<GameObject> vegetationList = chunkVegetation[coord];
        
        foreach (var placement in placementCache)
        {
            // Configure pool if needed
            if (!configuredPools.Contains(placement.prefabData.Prefab))
            {
                ConfigurePool(placement.prefabData);
                configuredPools.Add(placement.prefabData.Prefab);
            }
            
            // Spawn from pool
            GameObject vegetation = PoolManager.Instance.Spawn(
                placement.prefabData.Prefab,
                placement.position,
                placement.rotation,
                vegetationParent
            );
            
            if (vegetation != null)
            {
                // Apply scale
                vegetation.transform.localScale = Vector3.one * placement.scale;
                
                // Apply flip
                if (placement.flipX)
                {
                    SpriteRenderer sr = vegetation.GetComponent<SpriteRenderer>();
                    if (sr != null)
                    {
                        sr.flipX = true;
                    }
                }
                
                // Ensure Y-sorting is set up
                EnsureYSorting(vegetation, placement.prefabData);
                
                // Track this vegetation object
                vegetationList.Add(vegetation);
            }
        }
        
        if (debugMode)
            Debug.Log($"Spawned {vegetationList.Count} vegetation objects in chunk {coord}");
    }
    
    private void ConfigurePool(VegetationPrefabData prefabData)
    {
        // Pool will be auto-created by PoolManager, but we can pre-warm it
        // by spawning and immediately despawning objects
        List<GameObject> tempObjects = new List<GameObject>(prefabData.InitialPoolSize);
        
        for (int i = 0; i < prefabData.InitialPoolSize; i++)
        {
            GameObject obj = PoolManager.Instance.Spawn(
                prefabData.Prefab,
                Vector3.zero,
                Quaternion.identity,
                vegetationParent
            );
            
            if (obj != null)
                tempObjects.Add(obj);
        }
        
        // Immediately return them to the pool
        foreach (var obj in tempObjects)
        {
            PoolManager.Instance.Despawn(obj);
        }
        
        if (debugMode)
            Debug.Log($"Pre-warmed pool for {prefabData.VegetationName} with {prefabData.InitialPoolSize} objects");
    }
    
    private void EnsureYSorting(GameObject vegetation, VegetationPrefabData prefabData)
    {
        YSortingController ySorting = vegetation.GetComponent<YSortingController>();
        
        if (ySorting == null)
        {
            ySorting = vegetation.AddComponent<YSortingController>();
        }
        
        // Apply Y-sorting offset from prefab data
        ySorting.SetYOffset(prefabData.YSortingOffset);
        
        // Apply sorting order offset if needed
        SpriteRenderer sr = vegetation.GetComponent<SpriteRenderer>();
        if (sr != null && prefabData.SortingOrderOffset != 0)
        {
            sr.sortingOrder += prefabData.SortingOrderOffset;
        }
    }
    
    private void DespawnVegetationForChunk(ChunkCoordinate coord)
    {
        if (!chunkVegetation.ContainsKey(coord))
            return;
            
        List<GameObject> vegetationList = chunkVegetation[coord];
        
        foreach (var vegetation in vegetationList)
        {
            if (vegetation != null)
            {
                PoolManager.Instance.Despawn(vegetation);
            }
        }
        
        vegetationList.Clear();
        chunkVegetation.Remove(coord);
    }
    
    public void ClearAllVegetation()
    {
        chunksToRemove.Clear();
        chunksToRemove.AddRange(chunkVegetation.Keys);
        
        foreach (var coord in chunksToRemove)
        {
            DespawnVegetationForChunk(coord);
        }
        
        chunkVegetation.Clear();
    }
    
    #if UNITY_EDITOR
    [Button("Debug: Spawn Test Vegetation", ButtonSizes.Large)]
    void DebugSpawnTestVegetation()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only test in play mode!");
            return;
        }
        
        ChunkCoordinate currentChunk = chunkManager.GetCurrentPlayerChunk();
        OnChunkGenerated(currentChunk, Random.Range(0, 10000));
    }
    
    [Button("Debug: Clear All Vegetation", ButtonSizes.Large)]
    void DebugClearAllVegetation()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only test in play mode!");
            return;
        }
        
        ClearAllVegetation();
        Debug.Log("Cleared all vegetation");
    }
    
    [Button("Debug: Log Vegetation Stats", ButtonSizes.Medium)]
    public void DebugLogStats()
    {
        Debug.Log("=== Vegetation Stats ===");
        Debug.Log($"Active Chunks: {chunkVegetation.Count}");
        
        int totalVegetation = 0;
        foreach (var kvp in chunkVegetation)
        {
            int count = kvp.Value.Count;
            totalVegetation += count;
            Debug.Log($"  Chunk {kvp.Key}: {count} vegetation objects");
        }
        
        Debug.Log($"Total Vegetation Objects: {totalVegetation}");
        Debug.Log($"Configured Pools: {configuredPools.Count}");
        Debug.Log("========================");
    }
    #endif
}