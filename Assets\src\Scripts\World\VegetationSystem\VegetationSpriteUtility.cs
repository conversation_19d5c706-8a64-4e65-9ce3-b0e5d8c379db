using UnityEngine;
using UnityEngine.U2D;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Utility class for vegetation-specific sprite operations and atlas management.
/// Provides helper methods for working with vegetation sprites and atlases.
/// </summary>
public static class VegetationSpriteUtility
{
    // Common vegetation sprite naming patterns
    public static class NamingPatterns
    {
        public const string TALL_GRASS = "tall_grass";
        public const string SHORT_GRASS = "short_grass";
        public const string BUSH = "bush";
        public const string FLOWER = "flower";
        public const string TREE = "tree";
        public const string FERN = "fern";
        public const string MUSHROOM = "mushroom";
    }
    
    /// <summary>
    /// Creates a VegetationType from sprites in an atlas matching a pattern
    /// </summary>
    public static VegetationType CreateVegetationTypeFromAtlas(
        SpriteAtlas atlas, 
        string namePattern, 
        string typeName, 
        string category = "Grass",
        string description = "")
    {
        if (atlas == null || string.IsNullOrEmpty(namePattern))
        {
            Debug.LogError("VegetationSpriteUtility: Invalid parameters for CreateVegetationTypeFromAtlas");
            return null;
        }
        
        // Get sprites matching the pattern
        Sprite[] matchingSprites = GetSpritesFromAtlasWithPattern(atlas, namePattern);
        
        if (matchingSprites.Length == 0)
        {
            Debug.LogWarning($"VegetationSpriteUtility: No sprites found matching pattern '{namePattern}' in atlas '{atlas.name}'");
            return null;
        }
        
        // Create VegetationType ScriptableObject
        VegetationType vegetationType = ScriptableObject.CreateInstance<VegetationType>();
        
        // Use reflection to set private fields (since we can't access them directly)
        var typeNameField = typeof(VegetationType).GetField("typeName", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var categoryField = typeof(VegetationType).GetField("category", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var descriptionField = typeof(VegetationType).GetField("description", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var spriteVariantsField = typeof(VegetationType).GetField("spriteVariants", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        typeNameField?.SetValue(vegetationType, typeName);
        categoryField?.SetValue(vegetationType, category);
        descriptionField?.SetValue(vegetationType, description);
        spriteVariantsField?.SetValue(vegetationType, matchingSprites);
        
        return vegetationType;
    }
    
    /// <summary>
    /// Gets sprites from an atlas that match a naming pattern
    /// </summary>
    public static Sprite[] GetSpritesFromAtlasWithPattern(SpriteAtlas atlas, string pattern)
    {
        if (atlas == null || string.IsNullOrEmpty(pattern))
            return new Sprite[0];
            
        if (SpriteAtlasManager.Instance != null)
        {
            return SpriteAtlasManager.Instance.GetSpritesWithPattern(atlas, pattern);
        }
        
        // Fallback if SpriteAtlasManager is not available
        Sprite[] allSprites = new Sprite[atlas.spriteCount];
        atlas.GetSprites(allSprites);
        
        return allSprites.Where(s => s != null && s.name.Contains(pattern)).ToArray();
    }
    
    /// <summary>
    /// Gets sprites from an atlas with a specific prefix
    /// </summary>
    public static Sprite[] GetSpritesFromAtlasWithPrefix(SpriteAtlas atlas, string prefix)
    {
        if (atlas == null || string.IsNullOrEmpty(prefix))
            return new Sprite[0];
            
        if (SpriteAtlasManager.Instance != null)
        {
            return SpriteAtlasManager.Instance.GetSpritesWithPrefix(atlas, prefix);
        }
        
        // Fallback if SpriteAtlasManager is not available
        Sprite[] allSprites = new Sprite[atlas.spriteCount];
        atlas.GetSprites(allSprites);
        
        return allSprites.Where(s => s != null && s.name.StartsWith(prefix)).ToArray();
    }
    
    /// <summary>
    /// Validates that a sprite is suitable for vegetation use
    /// </summary>
    public static bool IsValidVegetationSprite(Sprite sprite)
    {
        if (sprite == null)
            return false;
            
        // Check if sprite has reasonable dimensions for vegetation
        Rect rect = sprite.rect;
        if (rect.width <= 0 || rect.height <= 0)
            return false;
            
        // Check if sprite is too large (might be a background element)
        if (rect.width > 512 || rect.height > 512)
        {
            Debug.LogWarning($"VegetationSpriteUtility: Sprite '{sprite.name}' is very large ({rect.width}x{rect.height}) - might not be suitable for vegetation");
        }
        
        return true;
    }
    
    /// <summary>
    /// Filters sprites to only include valid vegetation sprites
    /// </summary>
    public static Sprite[] FilterValidVegetationSprites(Sprite[] sprites)
    {
        if (sprites == null)
            return new Sprite[0];
            
        return sprites.Where(IsValidVegetationSprite).ToArray();
    }
    
    /// <summary>
    /// Groups sprites by common naming patterns
    /// </summary>
    public static Dictionary<string, List<Sprite>> GroupSpritesByPattern(Sprite[] sprites)
    {
        var groups = new Dictionary<string, List<Sprite>>();
        
        if (sprites == null)
            return groups;
            
        foreach (var sprite in sprites)
        {
            if (sprite == null) continue;
            
            string pattern = DetermineVegetationPattern(sprite.name);
            
            if (!groups.ContainsKey(pattern))
            {
                groups[pattern] = new List<Sprite>();
            }
            
            groups[pattern].Add(sprite);
        }
        
        return groups;
    }
    
    /// <summary>
    /// Determines the vegetation pattern from a sprite name
    /// </summary>
    public static string DetermineVegetationPattern(string spriteName)
    {
        if (string.IsNullOrEmpty(spriteName))
            return "unknown";
            
        string lowerName = spriteName.ToLower();
        
        if (lowerName.Contains("tall") && lowerName.Contains("grass"))
            return NamingPatterns.TALL_GRASS;
        if (lowerName.Contains("short") && lowerName.Contains("grass"))
            return NamingPatterns.SHORT_GRASS;
        if (lowerName.Contains("grass"))
            return NamingPatterns.SHORT_GRASS; // Default grass type
        if (lowerName.Contains("bush"))
            return NamingPatterns.BUSH;
        if (lowerName.Contains("flower"))
            return NamingPatterns.FLOWER;
        if (lowerName.Contains("tree"))
            return NamingPatterns.TREE;
        if (lowerName.Contains("fern"))
            return NamingPatterns.FERN;
        if (lowerName.Contains("mushroom"))
            return NamingPatterns.MUSHROOM;
            
        return "unknown";
    }
    
    /// <summary>
    /// Creates uniform weights for sprite selection
    /// </summary>
    public static float[] CreateUniformWeights(int count)
    {
        if (count <= 0)
            return new float[0];
            
        float[] weights = new float[count];
        float uniformWeight = 1f / count;
        
        for (int i = 0; i < count; i++)
        {
            weights[i] = uniformWeight;
        }
        
        return weights;
    }
    
    /// <summary>
    /// Creates weighted distribution favoring certain indices
    /// </summary>
    public static float[] CreateWeightedDistribution(int count, int[] favoredIndices, float favorWeight = 2f)
    {
        if (count <= 0)
            return new float[0];
            
        float[] weights = new float[count];
        float normalWeight = 1f;
        
        // Set normal weights
        for (int i = 0; i < count; i++)
        {
            weights[i] = normalWeight;
        }
        
        // Apply favor weights
        if (favoredIndices != null)
        {
            foreach (int index in favoredIndices)
            {
                if (index >= 0 && index < count)
                {
                    weights[index] = favorWeight;
                }
            }
        }
        
        return weights;
    }
    
    /// <summary>
    /// Logs information about sprites in an atlas
    /// </summary>
    public static void LogAtlasInfo(SpriteAtlas atlas)
    {
        if (atlas == null)
        {
            Debug.LogError("VegetationSpriteUtility: Cannot log info for null atlas");
            return;
        }
        
        Sprite[] sprites = new Sprite[atlas.spriteCount];
        atlas.GetSprites(sprites);
        
        var groups = GroupSpritesByPattern(sprites);
        
        Debug.Log($"=== Atlas Info: {atlas.name} ===");
        Debug.Log($"Total Sprites: {sprites.Length}");
        Debug.Log($"Valid Vegetation Sprites: {FilterValidVegetationSprites(sprites).Length}");
        
        foreach (var group in groups)
        {
            Debug.Log($"  {group.Key}: {group.Value.Count} sprites");
        }
        
        Debug.Log("================================");
    }
}
