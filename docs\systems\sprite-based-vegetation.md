# Sprite-Based Vegetation System

## Overview

The sprite-based vegetation system provides a flexible approach to vegetation spawning that uses a single generic prefab with dynamic sprite assignment from spritesheets. This system allows for efficient memory usage and easy content expansion without creating individual prefabs for each vegetation variant.

## Key Components

### 1. VegetationType ScriptableObject
- **Purpose**: Defines a category of vegetation with associated sprites and properties
- **Location**: `Assets/Data/VegetationType.cs`
- **Features**:
  - Sprite variants from spritesheets
  - Weighted sprite selection
  - Animation support (idle and wind)
  - Visual properties (sorting, tinting, scaling)

### 2. Enhanced VegetationPrefabData
- **Purpose**: Extended to support both traditional and sprite-based systems
- **Location**: `Assets/Data/VegetationPrefabData.cs`
- **New Features**:
  - Sprite-based system toggle
  - VegetationType reference
  - Scale multiplier overrides
  - Backward compatibility

### 3. Enhanced VegetationInstance
- **Purpose**: Handles dynamic sprite assignment and configuration
- **Location**: `Assets/src/Scripts/World/VegetationSystem/VegetationInstance.cs`
- **New Features**:
  - Dynamic sprite assignment from VegetationType
  - Animation integration with SpriteAnimator
  - State management for pooling

### 4. Sprite Atlas Management
- **SpriteAtlasManager**: `Assets/src/Scripts/Utilities/SpriteAtlasManager.cs`
- **VegetationSpriteUtility**: `Assets/src/Scripts/World/VegetationSystem/VegetationSpriteUtility.cs`
- **Features**:
  - Efficient sprite atlas caching
  - Pattern-based sprite grouping
  - Vegetation-specific utilities

## Setup Guide

### 1. Create VegetationType Assets

```csharp
// Example: Creating a tall grass vegetation type
1. Right-click in Project → Create → 2D Rogue → Vegetation Type
2. Configure the VegetationType:
   - Set Type Name: "Tall Grass"
   - Set Category: "Grass"
   - Add sprite variants from your spritesheet
   - Configure weights (optional)
   - Set visual properties (tint, scale, sorting)
```

### 2. Configure BiomeData for Sprite-Based System

```csharp
// In BiomeData inspector:
1. Enable "Use Sprite Based Vegetation"
2. Assign "Generic Vegetation Prefab" (use GenericVegetationPrefab.prefab)
3. Add VegetationType assets to:
   - High Grass Vegetation Types
   - Low Grass Vegetation Types
   - Additional Vegetation Types (bushes, flowers, etc.)
4. Set Additional Vegetation Chance (0-1)
```

### 3. Generic Vegetation Prefab Setup

The system includes a pre-configured `GenericVegetationPrefab.prefab` with:
- **SpriteRenderer**: For displaying sprites
- **VegetationInstance**: Handles sprite-based configuration
- **SpriteAnimator**: For animation support
- **YSortingController**: For proper depth sorting

## Usage Examples

### Creating VegetationType from Sprite Atlas

```csharp
// Using VegetationSpriteUtility
VegetationType grassType = VegetationSpriteUtility.CreateVegetationTypeFromAtlas(
    myAtlas, 
    "tall_grass", // Pattern to match
    "Tall Grass", // Type name
    "Grass",      // Category
    "Tall grass variants for meadow biomes"
);
```

### Runtime Sprite Assignment

```csharp
// VegetationInstance automatically handles this during spawn
public void ConfigureWithPrefabData(VegetationPrefabData prefabData)
{
    if (prefabData.UseSpriteBasedSystem)
    {
        // Get random sprite from vegetation type
        Sprite randomSprite = prefabData.GetRandomSprite();
        spriteRenderer.sprite = randomSprite;
        
        // Apply visual properties
        spriteRenderer.color = prefabData.GetTintColor();
        transform.localScale *= prefabData.GetEffectiveScaleMultiplier();
    }
}
```

## System Integration

### Biome Integration
- BiomeData supports both traditional and sprite-based systems
- Automatic fallback to traditional system if sprite-based is not configured
- New methods: `GetRandomVegetationType()`, `HasSpriteBasedVegetation()`

### Spawn Manager Integration
- VegetationSpawnManager automatically detects system type
- Creates temporary VegetationPrefabData for sprite-based vegetation
- Maintains deterministic spawning with both systems

### Pool System Compatibility
- Works seamlessly with existing PoolManager
- VegetationInstance handles proper reset on despawn
- Efficient memory usage through sprite reuse

## Performance Considerations

### Memory Efficiency
- Single generic prefab reduces memory overhead
- Sprite atlas caching minimizes texture memory usage
- Pool system reuses GameObjects efficiently

### Runtime Performance
- Sprite assignment happens only during spawn
- Cached sprite lookups for fast access
- Minimal garbage collection impact

## Migration Guide

### From Traditional to Sprite-Based System

1. **Backup existing setup**
2. **Create VegetationType assets** for your vegetation categories
3. **Update BiomeData**:
   - Enable sprite-based system
   - Assign generic prefab
   - Configure vegetation types
4. **Test thoroughly** with existing biomes

### Hybrid Approach
- Both systems can coexist
- Different biomes can use different systems
- Gradual migration is supported

## Troubleshooting

### Common Issues

1. **Sprites not appearing**:
   - Check VegetationType has valid sprite variants
   - Verify sprite atlas is properly configured
   - Ensure SpriteAtlasManager is initialized

2. **Animation not working**:
   - Verify SpriteAnimator component on prefab
   - Check VegetationType has animation assets assigned
   - Ensure animation assets are properly configured

3. **Performance issues**:
   - Use SpriteAtlasManager for efficient caching
   - Avoid creating VegetationType assets at runtime
   - Monitor pool sizes in VegetationPrefabData

### Debug Tools

- **VegetationPrefabData**: "Test Sprite Generation" button
- **SpriteAtlasManager**: Cache statistics and validation
- **VegetationType**: Configuration validation

## Best Practices

1. **Sprite Organization**:
   - Use consistent naming patterns
   - Group related sprites in same atlas
   - Keep sprite sizes reasonable for vegetation

2. **VegetationType Design**:
   - Create logical categories (grass, bushes, flowers)
   - Use weights for natural distribution
   - Test sprite variants in-game

3. **Performance Optimization**:
   - Preload commonly used atlases
   - Use appropriate pool sizes
   - Monitor memory usage with large sprite sets

4. **Content Creation**:
   - Design sprites with consistent visual style
   - Consider animation frame requirements
   - Plan for seasonal or biome variations
